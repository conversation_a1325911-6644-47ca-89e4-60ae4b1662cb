import React, { useState, useEffect, useCallback } from 'react';
import { 
  Grid, 
  Paper, 
  Box, 
  Typography, 
  Card, 
  CardContent,
  IconButton,
  Tooltip,
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  Chip,
  Alert
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  Settings as SettingsIcon,
  Fullscreen as FullscreenIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon
} from '@mui/icons-material';

// Map Components
import { Map<PERSON>ontainer, TileLayer, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>yline, CircleMarker } from 'react-leaflet';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';

// Custom Components
import RealTimeKPIs from '../components/organisms/RealTimeKPIs';
import OperationControlPanel from '../components/organisms/OperationControlPanel';
import VehicleStatusPanel from '../components/organisms/VehicleStatusPanel';
import AlertDashboard from '../components/organisms/AlertDashboard';
import RouteOptimizer from '../components/organisms/RouteOptimizer';
import HeatmapLayer from '../components/molecules/HeatmapLayer';

// Services
import { DashboardService } from '../services/DashboardService';
import { WebSocketService } from '../services/WebSocketService';

// Store
import { useDashboardStore } from '../store/dashboardStore';
import { useMapStore } from '../store/mapStore';

// Fix for default markers in react-leaflet
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: require('leaflet/dist/images/marker-icon-2x.png'),
  iconUrl: require('leaflet/dist/images/marker-icon.png'),
  shadowUrl: require('leaflet/dist/images/marker-shadow.png'),
});

const Dashboard = () => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [selectedOperation, setSelectedOperation] = useState(null);
  const [optimizedRoute, setOptimizedRoute] = useState(null);
  const [routeOptimizerOpen, setRouteOptimizerOpen] = useState(false);

  // Store hooks
  const {
    operations,
    kpis,
    alerts,
    isLoading,
    lastUpdate,
    fetchDashboardData,
    updateOperation
  } = useDashboardStore();

  const {
    mapCenter,
    mapZoom,
    heatmapData,
    selectedVehicle,
    setMapCenter,
    setSelectedVehicle
  } = useMapStore();

  // Custom icons for different operation types
  const operationIcons = {
    ride: L.icon({
      iconUrl: '/icons/passenger-icon.png',
      iconSize: [32, 32],
      iconAnchor: [16, 32],
      popupAnchor: [0, -32]
    }),
    parcel: L.icon({
      iconUrl: '/icons/parcel-icon.png',
      iconSize: [32, 32],
      iconAnchor: [16, 32],
      popupAnchor: [0, -32]
    }),
    mixed: L.icon({
      iconUrl: '/icons/mixed-icon.png',
      iconSize: [32, 32],
      iconAnchor: [16, 32],
      popupAnchor: [0, -32]
    }),
    emergency: L.icon({
      iconUrl: '/icons/emergency-icon.png',
      iconSize: [32, 32],
      iconAnchor: [16, 32],
      popupAnchor: [0, -32]
    })
  };

  // Load initial data
  useEffect(() => {
    fetchDashboardData();
  }, [fetchDashboardData]);

  // WebSocket subscriptions
  useEffect(() => {
    const subscriptions = [
      WebSocketService.subscribe('/topic/operations', (data) => {
        updateOperation(data);
      }),
      WebSocketService.subscribe('/topic/alerts', (alert) => {
        // Handle new alerts
        console.log('New alert:', alert);
      }),
      WebSocketService.subscribe('/topic/kpis', (kpiData) => {
        // Handle KPI updates
        console.log('KPI update:', kpiData);
      })
    ];

    return () => {
      subscriptions.forEach(sub => sub?.unsubscribe());
    };
  }, [updateOperation]);

  // Handle route optimization
  const handleOptimizeRoute = useCallback(async (vehicleId) => {
    try {
      const route = await DashboardService.optimizeRoute(vehicleId);
      setOptimizedRoute(route);
      setRouteOptimizerOpen(true);
    } catch (error) {
      console.error('Route optimization failed:', error);
    }
  }, []);

  // Handle operation selection
  const handleOperationClick = useCallback((operation) => {
    setSelectedOperation(operation);
    setMapCenter([operation.latitude, operation.longitude]);
  }, [setMapCenter]);

  // Refresh data
  const handleRefresh = useCallback(() => {
    fetchDashboardData();
  }, [fetchDashboardData]);

  // Toggle fullscreen
  const toggleFullscreen = useCallback(() => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  }, []);

  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case 'normal': return '#4caf50';
      case 'warning': return '#ff9800';
      case 'critical': return '#f44336';
      case 'emergency': return '#e91e63';
      default: return '#9e9e9e';
    }
  };

  // Get status icon
  const getStatusIcon = (status) => {
    switch (status) {
      case 'normal': return <CheckCircleIcon sx={{ color: '#4caf50' }} />;
      case 'warning': return <WarningIcon sx={{ color: '#ff9800' }} />;
      case 'critical': 
      case 'emergency': return <ErrorIcon sx={{ color: '#f44336' }} />;
      default: return <CheckCircleIcon sx={{ color: '#9e9e9e' }} />;
    }
  };

  return (
    <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* Header with KPIs */}
      <Paper sx={{ p: 2, mb: 1 }}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h4" component="h1" fontWeight="bold">
            🚗 لوحة تحكم العمليات المباشرة
          </Typography>
          <Box display="flex" gap={1}>
            <Tooltip title="تحديث البيانات">
              <IconButton onClick={handleRefresh} disabled={isLoading}>
                <RefreshIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="الإعدادات">
              <IconButton>
                <SettingsIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="ملء الشاشة">
              <IconButton onClick={toggleFullscreen}>
                <FullscreenIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>
        <RealTimeKPIs kpis={kpis} isLoading={isLoading} />
      </Paper>

      {/* Main Content */}
      <Box sx={{ flex: 1, display: 'flex', gap: 1 }}>
        {/* Left Sidebar */}
        <Paper sx={{ width: 320, p: 2, overflow: 'auto' }}>
          <OperationControlPanel />
          <Box mt={2}>
            <VehicleStatusPanel 
              onOptimize={handleOptimizeRoute}
              selectedVehicle={selectedVehicle}
              onVehicleSelect={setSelectedVehicle}
            />
          </Box>
        </Paper>

        {/* Map Area */}
        <Paper sx={{ flex: 1, position: 'relative', overflow: 'hidden' }}>
          <MapContainer 
            center={mapCenter} 
            zoom={mapZoom} 
            style={{ height: '100%', width: '100%' }}
            zoomControl={false}
          >
            <TileLayer
              url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
              attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            />

            {/* Heatmap Layer */}
            <HeatmapLayer data={heatmapData} />

            {/* Operations Markers */}
            {operations.map(operation => (
              <Marker
                key={operation.id}
                position={[operation.latitude, operation.longitude]}
                icon={operationIcons[operation.type] || operationIcons.ride}
                eventHandlers={{
                  click: () => handleOperationClick(operation)
                }}
              >
                <Popup>
                  <Box sx={{ minWidth: 200 }}>
                    <Typography variant="h6" gutterBottom>
                      {operation.type === 'ride' ? '🚗 رحلة ركاب' : 
                       operation.type === 'parcel' ? '📦 شحنة طرود' : 
                       operation.type === 'mixed' ? '🔄 مختلط' : '🚨 طوارئ'}
                    </Typography>
                    
                    <Box display="flex" alignItems="center" mb={1}>
                      <Typography variant="body2" sx={{ mr: 1 }}>الحالة:</Typography>
                      {getStatusIcon(operation.status)}
                      <Chip 
                        label={operation.status} 
                        size="small" 
                        sx={{ 
                          ml: 1,
                          backgroundColor: getStatusColor(operation.status),
                          color: 'white'
                        }}
                      />
                    </Box>

                    <Typography variant="body2" gutterBottom>
                      <strong>السائق:</strong> {operation.driver_name}
                    </Typography>
                    <Typography variant="body2" gutterBottom>
                      <strong>العميل:</strong> {operation.customer_name}
                    </Typography>
                    <Typography variant="body2" gutterBottom>
                      <strong>المدة:</strong> {operation.duration} دقيقة
                    </Typography>
                    <Typography variant="body2" gutterBottom>
                      <strong>المسافة المتبقية:</strong> {operation.distance_remaining} كم
                    </Typography>
                  </Box>
                </Popup>
              </Marker>
            ))}

            {/* Optimized Route */}
            {optimizedRoute && (
              <Polyline 
                positions={optimizedRoute} 
                color="#2196f3" 
                weight={4} 
                dashArray="10, 5"
                opacity={0.8}
              />
            )}

            {/* Demand Heatmap Points */}
            {heatmapData.map((point, index) => (
              <CircleMarker
                key={index}
                center={[point.lat, point.lng]}
                radius={Math.max(5, point.intensity * 10)}
                color={point.type === 'demand' ? '#f44336' : '#4caf50'}
                fillColor={point.type === 'demand' ? '#f44336' : '#4caf50'}
                fillOpacity={0.6}
                weight={2}
              >
                <Popup>
                  <Typography variant="body2">
                    {point.type === 'demand' ? '🔥 منطقة طلب عالي' : '✅ منطقة متاحة'}
                  </Typography>
                  <Typography variant="caption">
                    الكثافة: {point.intensity}
                  </Typography>
                </Popup>
              </CircleMarker>
            ))}
          </MapContainer>

          {/* Operation Details Overlay */}
          {selectedOperation && (
            <Card 
              sx={{ 
                position: 'absolute', 
                bottom: 16, 
                left: 16, 
                width: 320,
                zIndex: 1000
              }}
            >
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  تفاصيل العملية
                </Typography>
                <Grid container spacing={1}>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="textSecondary">
                      نوع العملية:
                    </Typography>
                    <Typography variant="body1" fontWeight="bold">
                      {selectedOperation.type === 'ride' ? 'ركاب' : 'طرود'}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="textSecondary">
                      الوقت المنقضي:
                    </Typography>
                    <Typography variant="body1" fontWeight="bold">
                      {selectedOperation.duration} دقيقة
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="textSecondary">
                      المسافة المتبقية:
                    </Typography>
                    <Typography variant="body1" fontWeight="bold">
                      {selectedOperation.distance_remaining} كم
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="textSecondary">
                      الوقت المتوقع:
                    </Typography>
                    <Typography variant="body1" fontWeight="bold">
                      {selectedOperation.eta} دقيقة
                    </Typography>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          )}
        </Paper>

        {/* Right Sidebar - Alerts */}
        <Paper sx={{ width: 320, p: 2, overflow: 'auto' }}>
          <AlertDashboard alerts={alerts} />
        </Paper>
      </Box>

      {/* Route Optimizer Dialog */}
      <Dialog 
        open={routeOptimizerOpen} 
        onClose={() => setRouteOptimizerOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>محسن المسارات الذكي</DialogTitle>
        <DialogContent>
          <RouteOptimizer 
            route={optimizedRoute}
            onClose={() => setRouteOptimizerOpen(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Floating Action Button for Emergency */}
      <Fab
        color="error"
        sx={{ position: 'fixed', bottom: 16, right: 16 }}
        onClick={() => {/* Handle emergency */}}
      >
        🚨
      </Fab>
    </Box>
  );
};

export default Dashboard;
