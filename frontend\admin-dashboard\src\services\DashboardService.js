import axios from 'axios';

class DashboardService {
  constructor() {
    this.baseURL = process.env.REACT_APP_API_URL || 'http://localhost:8080';
    this.liveOpsURL = process.env.REACT_APP_LIVE_OPS_URL || 'http://localhost:8100';
    
    // Create axios instances
    this.api = axios.create({
      baseURL: this.baseURL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.liveOpsApi = axios.create({
      baseURL: this.liveOpsURL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptors
    [this.api, this.liveOpsApi].forEach(instance => {
      instance.interceptors.request.use(
        (config) => {
          const token = localStorage.getItem('authToken');
          if (token) {
            config.headers.Authorization = `Bearer ${token}`;
          }
          return config;
        },
        (error) => Promise.reject(error)
      );

      instance.interceptors.response.use(
        (response) => response.data,
        (error) => {
          if (error.response?.status === 401) {
            localStorage.removeItem('authToken');
            window.location.href = '/login';
          }
          return Promise.reject(error);
        }
      );
    });
  }

  // Dashboard Data APIs
  async getDashboardData() {
    try {
      const [operations, kpis, alerts, systemStatus] = await Promise.all([
        this.getOperations(),
        this.getKPIs(),
        this.getAlerts(),
        this.getSystemStatus()
      ]);

      return {
        operations,
        kpis,
        alerts,
        systemStatus,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error);
      throw error;
    }
  }

  // Operations APIs
  async getOperations() {
    try {
      const response = await this.liveOpsApi.get('/api/live-ops/dashboard');
      return response.liveLocations || [];
    } catch (error) {
      console.error('Failed to fetch operations:', error);
      // Return mock data for development
      return this.getMockOperations();
    }
  }

  async getOperation(operationId) {
    return this.liveOpsApi.get(`/api/live-ops/operations/${operationId}`);
  }

  async updateOperation(operationId, data) {
    return this.liveOpsApi.put(`/api/live-ops/operations/${operationId}`, data);
  }

  // KPIs APIs
  async getKPIs() {
    try {
      const response = await this.liveOpsApi.get('/api/live-ops/dashboard');
      return {
        activeRides: response.activeRides || 0,
        availableDrivers: response.availableDrivers || 0,
        revenue: response.revenue || 0,
        avgWaitTime: response.averageWaitTime || 0,
        completedToday: response.completedToday || 0,
        pendingRequests: response.pendingRequests || 0,
        totalFleet: response.totalFleet || 0,
        customerSatisfaction: response.customerSatisfaction || 0,
        // Trends (mock data)
        activeRidesTrend: Math.floor(Math.random() * 20) - 10,
        availableDriversTrend: Math.floor(Math.random() * 20) - 10,
        revenueTrend: Math.floor(Math.random() * 30),
        avgWaitTimeTrend: Math.floor(Math.random() * 20) - 10
      };
    } catch (error) {
      console.error('Failed to fetch KPIs:', error);
      return this.getMockKPIs();
    }
  }

  async getRealtimeMetrics() {
    return this.liveOpsApi.get('/api/live-ops/analytics/realtime');
  }

  // Alerts APIs
  async getAlerts() {
    try {
      const response = await this.liveOpsApi.get('/api/live-ops/dashboard');
      return response.recentAlerts || [];
    } catch (error) {
      console.error('Failed to fetch alerts:', error);
      return this.getMockAlerts();
    }
  }

  async sendEmergencyAlert(alertData) {
    return this.liveOpsApi.post('/api/live-ops/alerts/emergency', alertData);
  }

  async markAlertAsRead(alertId) {
    return this.api.put(`/api/alerts/${alertId}/read`);
  }

  // Fleet APIs
  async getFleetStatus() {
    return this.liveOpsApi.get('/api/live-ops/fleet/status');
  }

  async getVehicleLocations() {
    return this.liveOpsApi.get('/api/live-ops/fleet/locations');
  }

  async updateDriverStatus(driverId, statusData) {
    return this.liveOpsApi.put(`/api/live-ops/drivers/${driverId}/status`, statusData);
  }

  // Route Optimization APIs
  async optimizeRoute(vehicleId) {
    try {
      const response = await this.liveOpsApi.post(`/api/live-ops/routes/optimize`, {
        vehicleId
      });
      return response.route || [];
    } catch (error) {
      console.error('Failed to optimize route:', error);
      // Return mock optimized route
      return this.getMockOptimizedRoute();
    }
  }

  async getRouteAnalytics(routeId) {
    return this.api.get(`/api/routes/${routeId}/analytics`);
  }

  // System Status APIs
  async getSystemStatus() {
    try {
      const [healthCheck, serviceStatus] = await Promise.all([
        this.liveOpsApi.get('/actuator/health'),
        this.api.get('/api/system/status')
      ]);

      return {
        overall: healthCheck.status === 'UP' ? 'healthy' : 'unhealthy',
        services: {
          liveOperations: healthCheck.status,
          apiGateway: serviceStatus.apiGateway || 'UP',
          database: serviceStatus.database || 'UP',
          redis: serviceStatus.redis || 'UP'
        },
        lastCheck: new Date().toISOString()
      };
    } catch (error) {
      console.error('Failed to fetch system status:', error);
      return {
        overall: 'unknown',
        services: {},
        lastCheck: new Date().toISOString()
      };
    }
  }

  // Analytics APIs
  async getHeatmapData() {
    try {
      const response = await this.api.get('/api/analytics/heatmap');
      return response.data || [];
    } catch (error) {
      console.error('Failed to fetch heatmap data:', error);
      return this.getMockHeatmapData();
    }
  }

  async getPerformanceMetrics(timeRange = '24h') {
    return this.api.get(`/api/analytics/performance?range=${timeRange}`);
  }

  // Mock Data Methods (for development)
  getMockOperations() {
    return [
      {
        id: 'OP001',
        type: 'ride',
        status: 'active',
        latitude: 24.7136,
        longitude: 46.6753,
        driver_name: 'أحمد محمد',
        customer_name: 'سارة أحمد',
        duration: 15,
        distance_remaining: 5.2,
        eta: 8,
        driverId: 'D001'
      },
      {
        id: 'OP002',
        type: 'parcel',
        status: 'pickup',
        latitude: 24.7236,
        longitude: 46.6853,
        driver_name: 'محمد علي',
        customer_name: 'فاطمة سالم',
        duration: 5,
        distance_remaining: 2.1,
        eta: 12,
        driverId: 'D002'
      },
      {
        id: 'OP003',
        type: 'ride',
        status: 'warning',
        latitude: 24.7036,
        longitude: 46.6653,
        driver_name: 'خالد أحمد',
        customer_name: 'نورا محمد',
        duration: 25,
        distance_remaining: 8.5,
        eta: 15,
        driverId: 'D003'
      }
    ];
  }

  getMockKPIs() {
    return {
      activeRides: 45,
      availableDrivers: 23,
      revenue: 2450.75,
      avgWaitTime: 3.2,
      completedToday: 156,
      pendingRequests: 8,
      totalFleet: 68,
      customerSatisfaction: 4.7,
      activeRidesTrend: 12,
      availableDriversTrend: -5,
      revenueTrend: 18,
      avgWaitTimeTrend: -8
    };
  }

  getMockAlerts() {
    return [
      {
        id: 'A001',
        severity: 'HIGH',
        message: 'السائق D001 تجاوز الحد الأقصى للسرعة',
        time: '2 دقيقة',
        read: false,
        timestamp: new Date(Date.now() - 2 * 60 * 1000).toISOString()
      },
      {
        id: 'A002',
        severity: 'MEDIUM',
        message: 'طلب عالي في منطقة وسط المدينة',
        time: '5 دقائق',
        read: false,
        timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString()
      },
      {
        id: 'A003',
        severity: 'LOW',
        message: 'المركبة V045 تحتاج صيانة',
        time: '10 دقائق',
        read: true,
        timestamp: new Date(Date.now() - 10 * 60 * 1000).toISOString()
      }
    ];
  }

  getMockHeatmapData() {
    return [
      { lat: 24.7136, lng: 46.6753, intensity: 0.8, type: 'demand' },
      { lat: 24.7236, lng: 46.6853, intensity: 0.6, type: 'supply' },
      { lat: 24.7036, lng: 46.6653, intensity: 0.9, type: 'demand' },
      { lat: 24.7336, lng: 46.6953, intensity: 0.4, type: 'supply' },
      { lat: 24.6936, lng: 46.6553, intensity: 0.7, type: 'demand' }
    ];
  }

  getMockOptimizedRoute() {
    return [
      [24.7136, 46.6753],
      [24.7156, 46.6773],
      [24.7176, 46.6793],
      [24.7196, 46.6813],
      [24.7216, 46.6833],
      [24.7236, 46.6853]
    ];
  }
}

export const DashboardService = new DashboardService();
export default DashboardService;
